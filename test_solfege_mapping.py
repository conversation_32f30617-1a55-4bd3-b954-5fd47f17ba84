#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音名和唱名的对应关系
验证第三线B4的唱名是否正确
"""

def test_solfege_mapping():
    """测试唱名映射"""
    print("🎵 测试音名和唱名对应关系")
    print("=" * 50)
    
    # 正确的音名和唱名对应关系
    correct_mapping = {
        'A': 'la',
        'B': 'si', 
        'C': 'do',
        'D': 're',
        'E': 'mi',
        'F': 'fa',
        'G': 'sol'
    }
    
    print("正确的音名唱名对应关系:")
    for note_name, solfege in correct_mapping.items():
        print(f"  {note_name} = {solfege}")
    
    print()
    
    # 测试程序中的映射
    try:
        from staff_practice_app_final import NoteInfo
        
        print("程序中的映射验证:")
        all_correct = True
        
        for note_name, expected_solfege in correct_mapping.items():
            # 创建测试音符
            note = NoteInfo(f"{note_name}4", note_name, 4, 440.0)
            actual_solfege = note.solfege
            
            if actual_solfege == expected_solfege:
                status = "✅"
            else:
                status = "❌"
                all_correct = False
            
            print(f"  {note_name} -> {actual_solfege} (期望: {expected_solfege}) {status}")
        
        print()
        
        if all_correct:
            print("✅ 所有音名唱名映射正确！")
        else:
            print("❌ 部分音名唱名映射有误")
        
        # 特别验证第三线B4
        print("\n🎯 特别验证第三线B4:")
        b4_note = NoteInfo("B4", "B", 4, 440.0)
        print(f"B4的唱名: {b4_note.solfege} (应该是: si)")
        
        if b4_note.solfege == "si":
            print("✅ 第三线B4的唱名正确！")
        else:
            print("❌ 第三线B4的唱名错误！")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_display_order():
    """测试显示顺序"""
    print("\n📋 测试显示顺序")
    print("=" * 30)
    
    print("当前程序显示:")
    print("音名: A B C D E F G")
    print("唱名: la si do re mi fa sol")
    print()
    
    print("对应关系验证:")
    note_names = ["A", "B", "C", "D", "E", "F", "G"]
    solfeges = ["la", "si", "do", "re", "mi", "fa", "sol"]
    
    correct_mapping = {
        'A': 'la', 'B': 'si', 'C': 'do', 'D': 're',
        'E': 'mi', 'F': 'fa', 'G': 'sol'
    }
    
    all_correct = True
    for i, note_name in enumerate(note_names):
        expected_solfege = correct_mapping[note_name]
        displayed_solfege = solfeges[i]
        
        if expected_solfege == displayed_solfege:
            status = "✅"
        else:
            status = "❌"
            all_correct = False
        
        print(f"位置{i+1}: {note_name} <-> {displayed_solfege} (期望: {expected_solfege}) {status}")
    
    if all_correct:
        print("\n✅ 显示顺序完全正确！")
    else:
        print("\n❌ 显示顺序有误")
    
    return all_correct

def test_third_line_specifically():
    """专门测试第三线"""
    print("\n🎼 专门测试第三线")
    print("=" * 30)
    
    print("五线谱第三线的音符:")
    print("- 音名: B4")
    print("- 唱名: si")
    print("- 位置: 第三线（中间线）")
    print()
    
    try:
        from staff_practice_app_final import NoteInfo
        
        # 创建B4音符
        b4_note = NoteInfo("B4", "B", 4, 440.0)
        
        print("程序中B4的信息:")
        print(f"- pitch: {b4_note.pitch}")
        print(f"- note_name: {b4_note.note_name}")
        print(f"- octave: {b4_note.octave}")
        print(f"- solfege: {b4_note.solfege}")
        print()
        
        if b4_note.solfege == "si":
            print("✅ 第三线B4的唱名正确是'si'！")
            return True
        else:
            print(f"❌ 第三线B4的唱名错误，显示为'{b4_note.solfege}'，应该是'si'")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎵 音名唱名对应关系验证")
    print("特别验证第三线B4的唱名")
    print()
    
    # 运行测试
    tests = [
        ("唱名映射", test_solfege_mapping),
        ("显示顺序", test_display_order),
        ("第三线专门测试", test_third_line_specifically),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 音名唱名对应关系完全正确！")
        print("\n✨ 验证结果:")
        print("- 第三线B4的唱名确实是'si' ✅")
        print("- 所有音名唱名映射正确 ✅")
        print("- 显示顺序正确 ✅")
        
        print("\n🎼 五线谱音名唱名对照:")
        print("A=la, B=si, C=do, D=re, E=mi, F=fa, G=sol")
        print("第三线B4 = si (不是sol)")
    else:
        print("⚠️  音名唱名对应关系需要修正")
    
    return passed == total

if __name__ == "__main__":
    main()
