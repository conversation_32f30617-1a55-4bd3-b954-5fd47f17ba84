#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音符位置映射是否正确
验证五线谱上的音符位置与实际音高是否对应
"""

def test_note_positions():
    """测试音符位置映射"""
    print("🎵 测试高音谱号音符位置映射")
    print("=" * 50)
    
    # 正确的高音谱号音符位置（从下到上）
    correct_positions = {
        # 下加线区域
        'C4': '下加一线',
        'D4': '下加一线下方',
        
        # 五线谱主体
        'E4': '第一线（最下面）',
        'F4': '第一间',
        'G4': '第二线',
        'A4': '第二间', 
        'B4': '第三线（中间线）',
        'C5': '第三间',
        'D5': '第四线',
        'E5': '第四间',
        'F5': '第五线（最上面）',
        
        # 上加线区域
        'G5': '第五线上方',
        'A5': '上加一线',
        'B5': '上加一线上方',
        'C6': '上加二线'
    }
    
    # 从程序中获取的位置映射
    program_positions = {
        # 下加线和下方音符
        'C4': 2.5,   # 下加一线
        'D4': 2,     # 下加一线下方
        'E4': 1.5,   # 第一线（最下面的线）
        'F4': 1,     # 第一线上方
        'G4': 0.5,   # 第二线
        'A4': -0.5,  # 第二线上方
        'B4': 0,     # 第三线（中间线）
        'C5': -0.5,  # 第三线上方
        'D5': -1,    # 第四线
        'E5': -1.5,  # 第四线上方
        'F5': -2,    # 第五线（最上面的线）
        'G5': -2.5,  # 第五线上方
        'A5': -3,    # 上加一线
        'B5': -3.5,  # 上加一线上方
        'C6': -4,    # 上加二线
    }
    
    print("音符位置验证:")
    print("音符\t位置描述\t\t程序值\t状态")
    print("-" * 60)
    
    all_correct = True
    
    for note, description in correct_positions.items():
        if note in program_positions:
            value = program_positions[note]
            
            # 验证逻辑：
            # - 正值表示在中间线下方
            # - 负值表示在中间线上方
            # - 0表示在中间线上（B4）
            
            status = "✅"
            if note == 'B4' and value != 0:
                status = "❌"
                all_correct = False
            elif note in ['C4', 'D4', 'E4', 'F4', 'G4'] and value <= 0:
                status = "❌" 
                all_correct = False
            elif note in ['A4', 'C5', 'D5', 'E5', 'F5', 'G5', 'A5', 'B5', 'C6'] and value >= 0:
                status = "❌"
                all_correct = False
            
            print(f"{note}\t{description:<20}\t{value}\t{status}")
        else:
            print(f"{note}\t{description:<20}\t缺失\t❌")
            all_correct = False
    
    print("\n" + "=" * 50)
    
    if all_correct:
        print("✅ 所有音符位置映射正确！")
        print("\n📝 位置说明:")
        print("- 中间线（B4）= 0")
        print("- 中间线下方 = 正值（+）")
        print("- 中间线上方 = 负值（-）")
        print("- 每个单位 = 一个线间距")
    else:
        print("❌ 部分音符位置映射有误，需要调整")
    
    return all_correct

def test_specific_notes():
    """测试特定音符"""
    print("\n🎯 重点音符测试")
    print("=" * 30)
    
    key_notes = {
        'C4': '下加一线 - 中央C',
        'E4': '第一线 - 五线谱最下面的线',
        'G4': '第二线',
        'B4': '第三线 - 中间线',
        'D5': '第四线', 
        'F5': '第五线 - 五线谱最上面的线'
    }
    
    for note, description in key_notes.items():
        print(f"♪ {note}: {description}")
    
    print("\n💡 提示:")
    print("在程序中点击钢琴键盘上的这些音符，")
    print("检查它们是否出现在五线谱的正确位置上。")

def main():
    """主函数"""
    print("🎼 五线谱音符位置验证工具")
    print("验证程序中的音符是否显示在正确的五线谱位置")
    print()
    
    # 运行测试
    result1 = test_note_positions()
    test_specific_notes()
    
    print(f"\n{'='*50}")
    if result1:
        print("🎉 音符位置映射验证通过！")
        print("\n现在可以运行主程序测试:")
        print("python staff_practice_app_final.py")
        print("\n测试方法:")
        print("1. 选择'钢琴键盘'模式")
        print("2. 点击C4键，应该显示在下加一线")
        print("3. 点击E4键，应该显示在第一线")
        print("4. 点击B4键，应该显示在中间线")
        print("5. 点击F5键，应该显示在第五线")
    else:
        print("⚠️ 音符位置映射需要进一步调整")
    
    return result1

if __name__ == "__main__":
    main()
