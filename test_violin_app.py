#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小提琴练习程序功能测试脚本
"""

import sys
import os
from violin_fingerboard import ViolinFingerboardData, ViolinPosition

def test_violin_fingerboard_data():
    """测试小提琴指板数据"""
    print("=== 测试小提琴指板数据 ===")
    
    fingerboard = ViolinFingerboardData()
    
    # 测试获取所有位置
    all_positions = fingerboard.get_all_positions()
    print(f"总共有 {len(all_positions)} 个位置")
    
    # 测试每根弦的位置
    strings = ['G', 'D', 'A', 'E']
    for string in strings:
        print(f"\n{string}弦:")
        for fret in range(5):
            position = fingerboard.get_position(string, fret)
            if position:
                finger_name = "空弦" if fret == 0 else f"{fret}指"
                sharp_mark = "#" if position.is_sharp else ""
                print(f"  {finger_name}: {position.note_name}{sharp_mark}{position.octave}")
    
    return True

def test_note_mapping():
    """测试音符映射"""
    print("\n=== 测试音符映射 ===")
    
    fingerboard = ViolinFingerboardData()
    
    # 测试特定位置
    test_cases = [
        ('G', 0, 'G3'),  # G弦空弦
        ('G', 1, 'G#3'), # G弦1指
        ('G', 2, 'A3'),  # G弦2指
        ('D', 0, 'D4'),  # D弦空弦
        ('A', 0, 'A4'),  # A弦空弦
        ('E', 0, 'E5'),  # E弦空弦
    ]
    
    for string, fret, expected_pitch in test_cases:
        position = fingerboard.get_position(string, fret)
        if position:
            actual_pitch = position.pitch
            status = "✅" if actual_pitch == expected_pitch else "❌"
            print(f"{status} {string}弦{fret}指: 期望{expected_pitch}, 实际{actual_pitch}")
        else:
            print(f"❌ {string}弦{fret}指: 未找到位置")
    
    return True

def test_random_selection():
    """测试随机选择功能"""
    print("\n=== 测试随机选择功能 ===")
    
    fingerboard = ViolinFingerboardData()
    all_positions = fingerboard.get_all_positions()
    
    # 过滤非空弦位置
    non_open_positions = [pos for pos in all_positions if pos.fret > 0]
    print(f"非空弦位置数量: {len(non_open_positions)}")
    
    # 显示一些随机位置
    import random
    print("随机选择的5个位置:")
    for i in range(5):
        pos = random.choice(non_open_positions)
        sharp_mark = "#" if pos.is_sharp else ""
        print(f"  {i+1}. {pos.string}弦{pos.fret}指: {pos.note_name}{sharp_mark}{pos.octave}")
    
    return True

def test_position_comparison():
    """测试位置比较逻辑"""
    print("\n=== 测试位置比较逻辑 ===")
    
    fingerboard = ViolinFingerboardData()
    
    # 获取一些测试位置
    pos1 = fingerboard.get_position('G', 2)  # A3
    pos2 = fingerboard.get_position('D', 3)  # F4
    pos3 = fingerboard.get_position('A', 2)  # B4
    
    if pos1 and pos2 and pos3:
        print(f"位置1: {pos1.string}弦{pos1.fret}指 = {pos1.note_name}{pos1.octave}")
        print(f"位置2: {pos2.string}弦{pos2.fret}指 = {pos2.note_name}{pos2.octave}")
        print(f"位置3: {pos3.string}弦{pos3.fret}指 = {pos3.note_name}{pos3.octave}")
        
        # 测试比较逻辑
        def compare_positions(p1, p2):
            return p1.note_name == p2.note_name and p1.octave == p2.octave
        
        print(f"位置1 == 位置1: {compare_positions(pos1, pos1)}")
        print(f"位置1 == 位置2: {compare_positions(pos1, pos2)}")
        print(f"位置2 == 位置3: {compare_positions(pos2, pos3)}")
    
    return True

def test_ui_components():
    """测试UI组件创建"""
    print("\n=== 测试UI组件创建 ===")
    
    try:
        import tkinter as tk
        from violin_fingerboard import ViolinFingerboard
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建画布
        canvas = tk.Canvas(root, width=300, height=400)
        
        # 创建指板
        fingerboard = ViolinFingerboard(canvas, 300, 400)
        
        print("✅ 指板组件创建成功")
        
        # 测试回调设置
        def test_callback(position):
            print(f"点击了: {position.string}弦{position.fret}指")
        
        fingerboard.set_position_click_callback(test_callback)
        print("✅ 回调函数设置成功")
        
        # 测试高亮功能
        fingerboard.highlight_position('G', 2)
        print("✅ 高亮功能测试成功")
        
        fingerboard.clear_highlight()
        print("✅ 清除高亮功能测试成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎻 小提琴练习程序功能测试 🎻")
    print("=" * 50)
    
    tests = [
        ("指板数据测试", test_violin_fingerboard_data),
        ("音符映射测试", test_note_mapping),
        ("随机选择测试", test_random_selection),
        ("位置比较测试", test_position_comparison),
        ("UI组件测试", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序功能正常。")
        print("\n使用说明:")
        print("1. 运行 'python violin_practice_app.py' 启动程序")
        print("2. 观察五线谱上的音符")
        print("3. 在右侧小提琴指板上点击对应位置")
        print("4. 正确答案 +1 分，错误答案 -1 分")
        print("5. 正确后会自动显示下一题")
        print("6. 使用空格键可以手动切换到下一题")
        print("7. 使用C键可以清除所有内容")
    else:
        print("⚠️  部分测试失败，请检查程序。")
    
    return passed == total

if __name__ == "__main__":
    main()
