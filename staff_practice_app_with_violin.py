#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五线谱视奏练习程序主应用类 - 集成小提琴指板
"""

import tkinter as tk
from tkinter import ttk, Canvas, Frame, Button, Label
import random
from typing import Optional

# 导入小提琴指板模块
from staff_practice_violin_fingerboard import ViolinFingerboard, ViolinPosition, violin_fingerboard_data

# 简化的音符数据类（兼容原有系统）
class NoteInfo:
    """音符信息类"""
    def __init__(self, pitch: str, note_name: str, octave: int, frequency: float = 440.0):
        self.pitch = pitch
        self.note_name = note_name
        self.octave = octave
        self.frequency = frequency
        self.solfege = self._get_solfege(note_name)
        self.is_black_key = '#' in pitch
    
    def _get_solfege(self, note_name: str) -> str:
        """获取唱名"""
        solfege_map = {
            'C': 'do', 'D': 're', 'E': 'mi', 'F': 'fa',
            'G': 'sol', 'A': 'la', 'B': 'si'
        }
        return solfege_map.get(note_name, '')

# 简化的音符数据库
class SimpleNoteDatabase:
    """简化的音符数据库"""
    
    def __init__(self):
        self.white_keys = []
        self._initialize_notes()
    
    def _initialize_notes(self):
        """初始化音符数据"""
        # 从小提琴指板数据中获取所有非空弦位置
        all_positions = violin_fingerboard_data.get_all_positions()
        non_open_positions = [pos for pos in all_positions if pos.fret > 0]
        
        for pos in non_open_positions:
            note = NoteInfo(
                pitch=pos.pitch,
                note_name=pos.note_name,
                octave=pos.octave
            )
            self.white_keys.append(note)
    
    def get_random_white_key(self) -> NoteInfo:
        """随机获取一个白键音符"""
        return random.choice(self.white_keys)

# 简化的五线谱渲染器
class SimpleStaffRenderer:
    """简化的五线谱渲染器"""
    
    def __init__(self, canvas: Canvas, width: int = 500, height: int = 200):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.staff_left = 80
        self.staff_right = width - 50
        self.staff_top = 50
        self.line_spacing = 15
        self.middle_line_y = self.staff_top + 2 * self.line_spacing
        self.current_note = None
        self._draw_staff()
    
    def _draw_staff(self):
        """绘制五线谱"""
        self.canvas.delete("all")
        
        # 绘制五条线
        for i in range(5):
            y = self.staff_top + i * self.line_spacing
            self.canvas.create_line(
                self.staff_left, y, self.staff_right, y,
                fill="black", width=1
            )
        
        # 绘制高音谱号
        self.canvas.create_text(
            self.staff_left + 20, self.middle_line_y,
            text="𝄞", font=("Arial", 40), fill="black"
        )
    
    def add_note(self, note_info):
        """添加音符到五线谱"""
        self.clear_notes()
        self.current_note = note_info
        
        # 音符位置映射（基于小提琴音域）
        note_positions = {
            'G3': 4, 'G#3': 4, 'A3': 3.5, 'A#3': 3.5, 'B3': 3,
            'C4': 2.5, 'C#4': 2.5, 'D4': 2, 'D#4': 2, 'E4': 1.5,
            'F4': 1, 'F#4': 1, 'G4': 0.5, 'G#4': 0.5, 'A4': 0,
            'A#4': 0, 'B4': -0.5, 'C5': -1, 'C#5': -1, 'D5': -1.5,
            'D#5': -1.5, 'E5': -2, 'F5': -2.5, 'F#5': -2.5, 'G5': -3, 'G#5': -3
        }
        
        y_offset = note_positions.get(note_info.pitch, 0) * self.line_spacing
        note_y = self.middle_line_y + y_offset
        note_x = self.staff_left + 120
        
        # 绘制加线（如果需要）
        self._draw_ledger_lines(note_x, note_y)
        
        # 绘制音符
        self.canvas.create_oval(
            note_x - 8, note_y - 6,
            note_x + 8, note_y + 6,
            fill="black", outline="black",
            tags="note"
        )
        
        # 绘制符干
        if note_y > self.middle_line_y:
            # 符干向上
            self.canvas.create_line(
                note_x + 8, note_y, note_x + 8, note_y - 30,
                fill="black", width=2,
                tags="note"
            )
        else:
            # 符干向下
            self.canvas.create_line(
                note_x - 8, note_y, note_x - 8, note_y + 30,
                fill="black", width=2,
                tags="note"
            )
    
    def _draw_ledger_lines(self, x: int, y: int):
        """绘制加线"""
        # 上加线
        if y < self.staff_top:
            line_y = self.staff_top - self.line_spacing
            while line_y >= y - 5:
                self.canvas.create_line(
                    x - 12, line_y, x + 12, line_y,
                    fill="black", width=1, tags="note"
                )
                line_y -= self.line_spacing
        
        # 下加线
        if y > self.staff_top + 4 * self.line_spacing:
            line_y = self.staff_top + 5 * self.line_spacing
            while line_y <= y + 5:
                self.canvas.create_line(
                    x - 12, line_y, x + 12, line_y,
                    fill="black", width=1, tags="note"
                )
                line_y += self.line_spacing
    
    def clear_notes(self):
        """清除音符"""
        self.canvas.delete("note")

class StaffPracticeAppWithViolin:
    """五线谱视奏练习应用程序 - 集成小提琴指板"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.setup_window()
        
        # 当前模式：'sight_reading' 或 'piano_keyboard' 或 'violin_practice'
        self.current_mode = 'sight_reading'
        
        # 当前显示的音符
        self.current_note: Optional[NoteInfo] = None
        
        # 是否显示答案
        self.show_answer = False
        
        # 计分系统
        self.score = 0
        
        # 音符数据库
        self.note_db = SimpleNoteDatabase()
        
        # 创建UI组件
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # 开始第一个练习
        self.next_note()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("五线谱视奏练习 - 集成小提琴指板")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
    
    def create_widgets(self):
        """创建所有UI组件"""
        # 主框架
        self.main_frame = Frame(self.root)
        
        # 标题
        self.title_label = Label(
            self.main_frame,
            text="五线谱视奏练习 - 集成小提琴指板",
            font=("Arial", 16, "bold")
        )
        
        # 模式选择框架
        self.mode_frame = Frame(self.main_frame)
        self.mode_var = tk.StringVar(value="sight_reading")
        
        self.sight_reading_radio = ttk.Radiobutton(
            self.mode_frame,
            text="视奏练习",
            variable=self.mode_var,
            value="sight_reading",
            command=self.on_mode_change
        )
        
        self.violin_practice_radio = ttk.Radiobutton(
            self.mode_frame,
            text="小提琴练习",
            variable=self.mode_var,
            value="violin_practice",
            command=self.on_mode_change
        )
        
        # 主要内容框架（左右分栏）
        self.content_frame = Frame(self.main_frame)
        
        # 左侧框架（五线谱和控制）
        self.left_frame = Frame(self.content_frame)
        
        # 五线谱画布
        self.staff_canvas = Canvas(
            self.left_frame,
            width=500, height=200,
            bg="white", relief="sunken", borderwidth=2
        )
        self.staff_renderer = SimpleStaffRenderer(self.staff_canvas, 500, 200)
        
        # 音名唱名显示框架
        self.note_info_frame = Frame(self.left_frame)
        
        # 音名显示
        self.note_name_label = Label(
            self.note_info_frame,
            text="音名: A B C D E F G",
            font=("Arial", 12)
        )
        
        # 唱名显示
        self.solfege_label = Label(
            self.note_info_frame,
            text="唱名: la si do re mi fa sol",
            font=("Arial", 12)
        )
        
        # 计分显示
        self.score_label = Label(
            self.note_info_frame,
            text="得分: 0",
            font=("Arial", 12, "bold"),
            fg="blue"
        )
        
        # 控制按钮框架
        self.control_frame = Frame(self.left_frame)
        
        self.next_button = Button(
            self.control_frame,
            text="下一题",
            command=self.next_note,
            font=("Arial", 10)
        )
        
        self.answer_button = Button(
            self.control_frame,
            text="显示答案",
            command=self.toggle_answer,
            font=("Arial", 10)
        )
        
        self.clear_button = Button(
            self.control_frame,
            text="清除",
            command=self.clear_all,
            font=("Arial", 10)
        )
        
        # 右侧框架（小提琴指板）
        self.right_frame = Frame(self.content_frame)
        
        # 小提琴指板画布
        self.violin_canvas = Canvas(
            self.right_frame,
            width=350, height=450,
            bg="lightyellow", relief="sunken", borderwidth=2
        )
        self.violin_fingerboard = ViolinFingerboard(self.violin_canvas, 350, 450)
        self.violin_fingerboard.set_position_click_callback(self.on_violin_position_click)

    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 标题
        self.title_label.pack(pady=(0, 10))

        # 模式选择
        self.mode_frame.pack(pady=(0, 10))
        self.sight_reading_radio.pack(side="left", padx=(0, 20))
        self.violin_practice_radio.pack(side="left")

        # 主要内容（左右分栏）
        self.content_frame.pack(fill="both", expand=True, pady=(0, 10))

        # 左侧内容
        self.left_frame.pack(side="left", fill="both", expand=True, padx=(0, 20))

        # 五线谱
        Label(self.left_frame, text="五线谱", font=("Arial", 12, "bold")).pack()
        self.staff_canvas.pack(pady=(5, 10))

        # 音名唱名信息
        self.note_info_frame.pack(pady=(0, 10))
        self.note_name_label.pack()
        self.solfege_label.pack()
        self.score_label.pack(pady=(5, 0))

        # 控制按钮
        self.control_frame.pack()
        self.next_button.pack(side="left", padx=(0, 10))
        self.answer_button.pack(side="left", padx=(0, 10))
        self.clear_button.pack(side="left")

        # 右侧内容（小提琴指板）
        self.right_frame.pack(side="right", fill="y")
        self.violin_canvas.pack()

    def bind_events(self):
        """绑定事件"""
        self.root.bind("<KeyPress-space>", lambda e: self.next_note())
        self.root.bind("<KeyPress-a>", lambda e: self.toggle_answer())
        self.root.bind("<KeyPress-c>", lambda e: self.clear_all())

    def on_mode_change(self):
        """模式切换处理"""
        self.current_mode = self.mode_var.get()

        if self.current_mode == "sight_reading":
            # 视奏练习模式
            self.next_button.config(state="normal")
            self.answer_button.config(state="normal")
            self.score_label.pack(pady=(5, 0))
            # 开始新的练习
            self.next_note()
        else:
            # 小提琴练习模式
            self.next_button.config(state="normal")
            self.answer_button.config(state="normal")
            self.score_label.pack(pady=(5, 0))
            # 开始新的练习
            self.next_note()

    def next_note(self):
        """显示下一个随机音符"""
        # 随机选择一个音符
        self.current_note = self.note_db.get_random_white_key()

        # 清除五线谱并显示新音符
        self.staff_renderer.clear_notes()
        self.staff_renderer.add_note(self.current_note)

        # 清除指板高亮
        self.violin_fingerboard.clear_highlight()

        # 隐藏答案
        self.show_answer = False
        self.answer_button.config(text="显示答案")
        self.update_note_info_display()

    def toggle_answer(self):
        """切换答案显示"""
        if not self.current_note:
            return

        self.show_answer = not self.show_answer
        self.answer_button.config(text="隐藏答案" if self.show_answer else "显示答案")
        self.update_note_info_display()

        if self.show_answer:
            # 高亮小提琴指板上的对应位置
            matches = violin_fingerboard_data.find_positions_by_note(
                self.current_note.note_name, self.current_note.octave
            )
            if matches:
                # 高亮第一个匹配的位置
                pos = matches[0]
                self.violin_fingerboard.highlight_position(pos.string, pos.fret)
        else:
            self.violin_fingerboard.clear_highlight()

    def on_violin_position_click(self, position: ViolinPosition):
        """处理小提琴指板位置点击"""
        if not self.current_note:
            return

        # 检查答案
        if (position.note_name == self.current_note.note_name and
            position.octave == self.current_note.octave):
            # 正确答案
            self.score += 1
            self.violin_fingerboard.highlight_position(position.string, position.fret)
            self.update_score_display()

            # 延迟生成下一题
            self.root.after(1000, self.next_note)
        else:
            # 错误答案
            self.score -= 1
            self.update_score_display()

    def update_note_info_display(self):
        """更新音名唱名显示"""
        if not self.current_note or not self.show_answer:
            # 显示所有音名和唱名
            self.note_name_label.config(text="音名: A B C D E F G")
            self.solfege_label.config(text="唱名: la si do re mi fa sol")
        else:
            # 高亮显示当前音符
            note_names = ["A", "B", "C", "D", "E", "F", "G"]
            solfeges = ["la", "si", "do", "re", "mi", "fa", "sol"]

            # 构建高亮显示的文本
            note_text = "音名: "
            solfege_text = "唱名: "

            for name in note_names:
                if name == self.current_note.note_name:
                    note_text += f"[{name}] "
                else:
                    note_text += f"{name} "

            for solfege in solfeges:
                if solfege == self.current_note.solfege:
                    solfege_text += f"[{solfege}] "
                else:
                    solfege_text += f"{solfege} "

            self.note_name_label.config(text=note_text.strip())
            self.solfege_label.config(text=solfege_text.strip())

    def update_score_display(self):
        """更新分数显示"""
        self.score_label.config(text=f"得分: {self.score}")

        # 根据分数改变颜色
        if self.score > 0:
            self.score_label.config(fg="green")
        elif self.score < 0:
            self.score_label.config(fg="red")
        else:
            self.score_label.config(fg="blue")

    def clear_all(self):
        """清除所有内容"""
        self.staff_renderer.clear_notes()
        self.violin_fingerboard.clear_highlight()
        self.current_note = None
        self.score = 0
        self.show_answer = False
        self.answer_button.config(text="显示答案")
        self.update_note_info_display()
        self.update_score_display()

def main():
    """主函数"""
    root = tk.Tk()
    app = StaffPracticeAppWithViolin(root)
    root.mainloop()

if __name__ == "__main__":
    main()
