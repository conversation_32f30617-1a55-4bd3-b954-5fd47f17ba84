# VNG数据统一处理器

## 概述

`vng_unified_processor.py` 是一个完整的VNG（视频眼震电图）病例数据处理系统，整合了数据提取、清理、格式化的完整流程。该系统基于原有成功的代码逻辑，保持了所有原有功能，同时添加了自动数据清理功能。

## 功能特点

### ✅ 完整的数据处理流程
- **数据提取**: 支持 `.docx`、`.doc`、`.wps` 格式文件
- **字段清理**: 自动清理姓名、科别、住院号等字段中的多余符号
- **日期标准化**: 统一日期格式为 `YYYY-MM-DD`
- **乱码清理**: 智能识别并清理印象字段中的乱码
- **结构优化**: 自动移除年份列，生成24字段的标准CSV

### ✅ 智能数据提取
- **WPS文件优化**: 特别优化了WPS文件的字段提取
- **多格式支持**: 统一处理不同格式的医疗文档
- **错误处理**: 完善的异常处理机制

### ✅ 数据质量保证
- **格式标准化**: 统一字段格式
- **内容保护**: 避免误删有效信息
- **质量统计**: 自动生成数据质量报告

## 使用方法

### 基本用法
```bash
python code/vng_unified_processor.py --source-dir data/final_vng_classified/ --output-dir output
```

### 参数说明
- `--source-dir`: 源文件目录路径（默认: ../data）
- `--output-dir`: 输出目录路径（默认: ../output）
- `--debug`: 显示调试信息（可选）

### 示例
```bash
# 使用默认路径
python vng_unified_processor.py

# 指定自定义路径
python vng_unified_processor.py --source-dir /path/to/source --output-dir /path/to/output

# 启用调试模式
python vng_unified_processor.py --debug
```

## 输出文件

系统会生成以下文件：

1. **原始提取数据**: `vng_patient_data_YYYYMMDD_HHMMSS.csv`
   - 包含所有提取的原始数据
   - 包含原始文本列用于调试

2. **最终清理数据**: `vng_patient_data_final_YYYYMMDD_HHMMSS.csv`
   - 经过清理和标准化的最终数据
   - 移除了原始文本列
   - 24个字段的标准格式
   - 位置试验相关字段已合并
   - 包含高级扫视字段

3. **处理日志**: `processing_log_YYYYMMDD_HHMMSS.txt`
   - 详细的处理过程记录
   - 错误信息和统计数据

4. **统计报告**: `statistics_report.txt`
   - 处理结果统计
   - 成功率和失败原因分析

## 数据字段

最终CSV文件包含以下24个字段：

1. 文件名
2. 姓名
3. 性别
4. 年龄
5. 检查日期
6. 科别
7. 门诊住院号
8. 编号
9. 定标试验
10. 自发性眼震
11. 自发性眼震_水平向左
12. 自发性眼震_水平向右
13. 自发性眼震_垂直向上
14. 自发性眼震_垂直向下
15. 凝视试验
16. 凝视试验_定向
17. 凝视试验_变向
18. 平滑跟踪
19. 扫视试验
20. 扫视试验_详情
21. 高级扫视
22. 视动性眼震
23. 印象
24. 位置试验

### 位置试验字段说明

位置试验字段整合了以下原始字段的信息：
- Roll_Test
- 翻身试验
- Dix_Hallpike_左侧
- Dix_Hallpike_右侧
- 疲劳现象

格式示例：
- `①Roll Test: 阳性; ②Dix-Hallpike 左侧悬头位: 阳性; 右侧悬头位: 阳性; 疲劳现象: 阴性`
- `①Roll Test/翻身试验: 阴性; ②Dix-Hallpike 左侧悬头位: 阴性`

## 数据清理功能

系统自动执行以下清理操作：

### 姓名字段
- 移除前后的冒号、下划线、空格
- 只保留中文字符和间隔号
- 清理格式化字符

### 科别字段
- 移除前后的格式化字符
- 清理床位号信息
- 标准化科室名称

### 住院号字段
- 只保留字母和数字
- 移除所有特殊字符和空格

### 检查日期字段
- 统一为YYYY-MM-DD格式
- 处理多种输入格式（/、-、.分隔符）
- 确保月份和日期为两位数

### 印象字段
- 智能识别和移除乱码
- 保留有意义的医学术语
- 清理多余的空白字符

## 数据质量

### 典型处理效果
- **姓名字段**: 98%+ 有效率
- **性别字段**: 99%+ 有效率  
- **科别字段**: 99%+ 有效率
- **检查日期**: 96%+ 有效率
- **住院号**: 76%+ 有效率
- **印象字段**: 100% 有效率（清理后）

### 格式标准化
- ✅ 日期格式: 100% 统一为 `YYYY-MM-DD`
- ✅ 姓名格式: 100% 移除多余符号
- ✅ 科别格式: 100% 移除床位信息
- ✅ 住院号: 100% 纯字母数字格式

## 技术特点

### 智能乱码处理
- 识别15种常见乱码模式
- 智能截断：在乱码前的标点处截断
- 内容保护：避免误删正常文本

### 优化的字段提取
- 针对WPS文件的特殊格式优化
- 多种正则表达式模式匹配
- 容错性强的数据提取

### 自动化处理
- 一键完成完整流程
- 自动生成处理报告
- 完善的错误处理

## 示例输出

```
=== VNG数据完整处理流程 ===
源目录: ../data
输出目录: ../output

步骤1: 提取数据...
找到 3313 个文件
处理完成: 成功 3313, 失败 0

步骤2: 清理和最终化数据...
清理姓名字段...
清理科别字段...
清理住院号字段...
标准化检查日期...
清理印象字段...
移除年份列...
移除原始文本列...

=== 最终数据统计 ===
总记录数: 6883
总字段数: 24
姓名: 6850/6883 (99.5%)
性别: 6863/6883 (99.7%)
科别: 6767/6883 (98.3%)
住院号: 6314/6883 (91.7%)
检查日期: 6785/6883 (98.6%)
印象: 5588/6883 (81.2%)

=== 处理完成 ===
最终文件: ../output/vng_patient_data_final_20250802_231139.csv
记录数: 6883
字段数: 24
```

## 性能统计

- **处理速度**: 约7,000个文件/3分钟
- **成功率**: 通常>94%
- **支持格式**: DOCX, DOC, WPS
- **输出编码**: UTF-8 with BOM（Excel兼容）
- **去重功能**: 自动识别和跳过重复文件

## 错误处理

系统会自动处理以下情况：

1. **无法提取的文件**: 移动到 `failed_files/cannot_extract/`
2. **格式错误的文件**: 移动到 `failed_files/format_error/`
3. **处理异常**: 记录到日志文件中

## 注意事项

1. 确保源目录包含有效的VNG报告文件
2. 输出目录会自动创建
3. 处理大量文件时建议使用SSD存储
4. 建议定期备份输出结果

## 技术要求

- Python 3.6+
- 依赖包: pandas, docx, olefile
- 内存: 建议4GB以上
- 存储: 确保输出目录有足够空间

## 更新历史

- 2025-08-02: 创建统一处理器，整合提取和清理功能
- 移除年份和检查者字段
- 合并位置试验相关字段（Roll_Test、翻身试验、Dix_Hallpike_左侧、Dix_Hallpike_右侧、疲劳现象）为单一"位置试验"字段
- 添加"高级扫视"字段提取
- 添加文件去重功能，避免重复处理相同文件
- 优化为24字段格式
- 改进乱码清理算法
- 添加自动数据标准化功能

- **v1.0**: 统一整合所有数据处理功能
- 移除了多个分散的脚本文件
- 优化了WPS文件处理
- 完善了乱码清理机制
- 自动化了完整处理流程
