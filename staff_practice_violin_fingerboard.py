#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小提琴指板视图模块 - 集成到五线谱练习程序
显示小提琴第一把位的指板图
"""

import tkinter as tk
from tkinter import Canvas
from typing import Dict, List, Tuple, Optional, Callable
from dataclasses import dataclass

@dataclass
class ViolinPosition:
    """小提琴指板位置信息"""
    string: str          # 弦名：G, D, A, E
    fret: int           # 品位：0-4 (0=空弦, 1-4=第1-4指)
    note_name: str      # 音名：如 'G', 'A', 'B', 'C', 'D', 'E', 'F'
    octave: int         # 八度
    pitch: str          # 完整音高标记：如 'G3', 'A3'
    is_sharp: bool      # 是否为升号
    finger: int         # 手指编号：0=空弦, 1-4=第1-4指

class ViolinFingerboardData:
    """小提琴指板数据"""
    
    def __init__(self):
        """初始化小提琴指板数据"""
        self.positions: Dict[Tuple[str, int], ViolinPosition] = {}
        self._initialize_positions()
    
    def _initialize_positions(self):
        """初始化所有指板位置"""
        # 小提琴四根弦的空弦音高
        open_strings = {
            'G': ('G', 3),  # G3
            'D': ('D', 4),  # D4
            'A': ('A', 4),  # A4
            'E': ('E', 5)   # E5
        }
        
        # 每根弦上的音符序列（半音阶）
        chromatic_notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        
        for string_name, (base_note, base_octave) in open_strings.items():
            # 找到基础音符在半音阶中的位置
            base_index = chromatic_notes.index(base_note)
            
            # 为每个品位生成音符
            for fret in range(5):  # 0-4品位
                # 计算当前音符索引
                note_index = (base_index + fret) % 12
                note_name = chromatic_notes[note_index]
                
                # 计算八度
                octave = base_octave
                if base_index + fret >= 12:
                    octave += 1
                
                # 判断是否为升号
                is_sharp = '#' in note_name
                display_note = note_name.replace('#', '')
                
                # 创建位置对象
                position = ViolinPosition(
                    string=string_name,
                    fret=fret,
                    note_name=display_note,
                    octave=octave,
                    pitch=f"{note_name}{octave}",  # 保留完整的音符名称包括#
                    is_sharp=is_sharp,
                    finger=fret
                )
                
                self.positions[(string_name, fret)] = position
    
    def get_position(self, string: str, fret: int) -> Optional[ViolinPosition]:
        """获取指定位置的音符信息"""
        return self.positions.get((string, fret))
    
    def get_all_positions(self) -> List[ViolinPosition]:
        """获取所有位置"""
        return list(self.positions.values())
    
    def find_positions_by_note(self, note_name: str, octave: int) -> List[ViolinPosition]:
        """根据音名和八度查找所有匹配的位置"""
        matches = []
        for position in self.positions.values():
            if position.note_name == note_name and position.octave == octave:
                matches.append(position)
        return matches

class ViolinFingerboard:
    """小提琴指板视图"""
    
    def __init__(self, canvas: Canvas, width: int = 300, height: int = 400):
        """
        初始化小提琴指板视图
        
        Args:
            canvas: Tkinter Canvas对象
            width: 指板宽度
            height: 指板高度
        """
        self.canvas = canvas
        self.width = width
        self.height = height
        
        # 指板数据
        self.fingerboard_data = ViolinFingerboardData()
        
        # 布局参数
        self.margin_top = 30
        self.margin_bottom = 30
        self.margin_left = 40
        self.margin_right = 40
        
        # 计算实际绘制区域
        self.board_width = width - self.margin_left - self.margin_right
        self.board_height = height - self.margin_top - self.margin_bottom
        
        # 弦间距和品位间距
        self.string_spacing = self.board_width // 3  # 4根弦，3个间距
        self.fret_spacing = self.board_height // 4   # 5个品位，4个间距
        
        # 弦的名称和位置
        self.strings = ['G', 'D', 'A', 'E']
        
        # 点击回调函数
        self.on_position_click: Optional[Callable[[ViolinPosition], None]] = None
        
        # 高亮位置
        self.highlighted_position: Optional[Tuple[str, int]] = None
        
        self._draw_fingerboard()
        self._bind_events()
    
    def _draw_fingerboard(self):
        """绘制指板"""
        # 清空画布
        self.canvas.delete("all")
        
        # 绘制标题
        self.canvas.create_text(
            self.width // 2, 15,
            text="小提琴第一把位",
            font=("Arial", 12, "bold"),
            fill="black"
        )
        
        # 绘制弦线
        self._draw_strings()
        
        # 绘制品位线
        self._draw_frets()
        
        # 绘制音符位置
        self._draw_positions()
        
        # 绘制弦名标签
        self._draw_string_labels()
        
        # 绘制品位标签
        self._draw_fret_labels()
    
    def _draw_strings(self):
        """绘制弦线"""
        for i, string_name in enumerate(self.strings):
            x = self.margin_left + i * self.string_spacing
            y1 = self.margin_top
            y2 = self.margin_top + self.board_height
            
            self.canvas.create_line(
                x, y1, x, y2,
                fill="brown", width=2,
                tags="string"
            )
    
    def _draw_frets(self):
        """绘制品位线"""
        for fret in range(5):  # 0-4品位
            y = self.margin_top + fret * self.fret_spacing
            x1 = self.margin_left
            x2 = self.margin_left + self.board_width
            
            # 空弦位置用粗线
            width = 3 if fret == 0 else 1
            color = "black" if fret == 0 else "gray"
            
            self.canvas.create_line(
                x1, y, x2, y,
                fill=color, width=width,
                tags="fret"
            )
    
    def _draw_positions(self):
        """绘制音符位置"""
        for i, string_name in enumerate(self.strings):
            for fret in range(5):
                position = self.fingerboard_data.get_position(string_name, fret)
                if position:
                    x = self.margin_left + i * self.string_spacing
                    y = self.margin_top + fret * self.fret_spacing
                    
                    # 跳过空弦位置的圆圈
                    if fret == 0:
                        continue
                    
                    # 调整位置到品位中间
                    if fret > 0:
                        y = y - self.fret_spacing // 2
                    
                    # 判断是否高亮
                    is_highlighted = (self.highlighted_position == (string_name, fret))
                    
                    # 绘制位置圆圈
                    radius = 15
                    fill_color = "yellow" if is_highlighted else ("lightblue" if position.is_sharp else "lightgreen")
                    outline_color = "red" if is_highlighted else "black"
                    
                    self.canvas.create_oval(
                        x - radius, y - radius,
                        x + radius, y + radius,
                        fill=fill_color, outline=outline_color, width=2,
                        tags=f"position_{string_name}_{fret}"
                    )
                    
                    # 绘制音符名称
                    display_text = position.note_name
                    if position.is_sharp:
                        display_text += "#"
                    
                    self.canvas.create_text(
                        x, y,
                        text=display_text,
                        font=("Arial", 10, "bold"),
                        fill="black",
                        tags=f"note_{string_name}_{fret}"
                    )
    
    def _draw_string_labels(self):
        """绘制弦名标签"""
        for i, string_name in enumerate(self.strings):
            x = self.margin_left + i * self.string_spacing
            y = self.margin_top - 15
            
            self.canvas.create_text(
                x, y,
                text=string_name,
                font=("Arial", 12, "bold"),
                fill="blue"
            )
    
    def _draw_fret_labels(self):
        """绘制品位标签"""
        labels = ["空弦", "1指", "2指", "3指", "4指"]
        for fret in range(5):
            x = self.margin_left - 25
            y = self.margin_top + fret * self.fret_spacing
            
            if fret > 0:
                y = y - self.fret_spacing // 2
            
            self.canvas.create_text(
                x, y,
                text=labels[fret],
                font=("Arial", 9),
                fill="purple"
            )
    
    def _bind_events(self):
        """绑定鼠标事件"""
        self.canvas.bind("<Button-1>", self._on_click)
    
    def _on_click(self, event):
        """处理鼠标点击事件"""
        clicked_position = self._get_position_at_coordinates(event.x, event.y)
        if clicked_position and self.on_position_click:
            self.on_position_click(clicked_position)
    
    def _get_position_at_coordinates(self, x: int, y: int) -> Optional[ViolinPosition]:
        """根据坐标获取对应的指板位置"""
        # 检查是否在指板范围内
        if (x < self.margin_left or x > self.margin_left + self.board_width or
            y < self.margin_top or y > self.margin_top + self.board_height):
            return None
        
        # 计算最近的弦
        string_index = round((x - self.margin_left) / self.string_spacing)
        if string_index < 0 or string_index >= len(self.strings):
            return None
        
        string_name = self.strings[string_index]
        
        # 计算最近的品位
        fret = round((y - self.margin_top) / self.fret_spacing)
        if fret < 0 or fret > 4:
            return None
        
        # 对于非空弦位置，检查是否点击在圆圈附近
        if fret > 0:
            expected_y = self.margin_top + fret * self.fret_spacing - self.fret_spacing // 2
            if abs(y - expected_y) > 20:  # 允许20像素的误差
                return None
        
        return self.fingerboard_data.get_position(string_name, fret)
    
    def highlight_position(self, string: str, fret: int):
        """高亮指定位置"""
        self.highlighted_position = (string, fret)
        self._draw_positions()
    
    def clear_highlight(self):
        """清除高亮"""
        self.highlighted_position = None
        self._draw_positions()
    
    def set_position_click_callback(self, callback: Callable[[ViolinPosition], None]):
        """设置位置点击回调函数"""
        self.on_position_click = callback

# 全局指板数据实例
violin_fingerboard_data = ViolinFingerboardData()
