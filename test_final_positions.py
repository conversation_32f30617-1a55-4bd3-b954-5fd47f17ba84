#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复后的音符位置
验证是否恢复到原来正确的逻辑
"""

def test_staff_y_positions():
    """测试staff_y位置映射"""
    print("🎼 测试恢复后的音符位置映射")
    print("=" * 50)
    
    # 原系统的正确staff_y值
    correct_staff_y = {
        'A3': -8,    # 下加二线
        'B3': -7,    # 下加一线和下加二线之间
        'C4': -6,    # 下加一线
        'D4': -5,    # 下加一线和第一线之间
        'E4': -4,    # 第一线
        'F4': -3,    # 第一间
        'G4': -2,    # 第二线
        'A4': -1,    # 第二间
        'B4': 0,     # 第三线（基准线）
        'C5': 1,     # 第三间
        'D5': 2,     # 第四线
        'E5': 3,     # 第四间
        'F5': 4,     # 第五线
        'G5': 5,     # 第五线上方
        'A5': 6,     # 上加一线
        'B5': 7,     # 上加一线和上加二线之间
        'C6': 8,     # 上加二线
    }
    
    # 程序中的staff_y值
    program_staff_y = {
        'A3': -8,    # 下加二线
        'A#3': -7,   # 下加一线和下加二线之间
        'B3': -7,    # 下加一线和下加二线之间
        'C4': -6,    # 下加一线
        'C#4': -6,   # 下加一线
        'D4': -5,    # 下加一线和第一线之间
        'D#4': -5,   # 下加一线和第一线之间
        'E4': -4,    # 第一线
        'F4': -3,    # 第一间
        'F#4': -3,   # 第一间
        'G4': -2,    # 第二线
        'G#4': -2,   # 第二线
        'A4': -1,    # 第二间
        'A#4': -1,   # 第二间
        'B4': 0,     # 第三线（基准线）
        'C5': 1,     # 第三间
        'C#5': 1,    # 第三间
        'D5': 2,     # 第四线
        'D#5': 2,    # 第四线
        'E5': 3,     # 第四间
        'F5': 4,     # 第五线
        'F#5': 4,    # 第五线
        'G5': 5,     # 第五线上方
        'G#5': 5,    # 第五线上方
        'A5': 6,     # 上加一线
        'A#5': 6,    # 上加一线
        'B5': 7,     # 上加一线和上加二线之间
        'C6': 8,     # 上加二线
    }
    
    print("音符位置验证:")
    print("音符\t程序值\t正确值\t状态\t位置描述")
    print("-" * 70)
    
    all_correct = True
    
    for note, correct_value in correct_staff_y.items():
        program_value = program_staff_y.get(note, "缺失")
        
        if program_value == correct_value:
            status = "✅"
        else:
            status = "❌"
            all_correct = False
        
        # 位置描述
        position_desc = {
            -8: "下加二线",
            -7: "下加一线间",
            -6: "下加一线",
            -5: "下一线间",
            -4: "第一线",
            -3: "第一间",
            -2: "第二线",
            -1: "第二间",
            0: "第三线",
            1: "第三间",
            2: "第四线",
            3: "第四间",
            4: "第五线",
            5: "第五线上",
            6: "上加一线",
            7: "上一线间",
            8: "上加二线"
        }.get(correct_value, "未知")
        
        print(f"{note}\t{program_value}\t{correct_value}\t{status}\t{position_desc}")
    
    print("\n" + "=" * 50)
    
    if all_correct:
        print("✅ 所有音符位置映射正确！")
        print("\n📝 计算公式验证:")
        print("y = middle_line_y - staff_y * half_spacing")
        print("- middle_line_y: 第三线的Y坐标")
        print("- staff_y: 音符的staff_y值")
        print("- half_spacing: line_spacing // 2")
        print("\n🎯 关键验证点:")
        print("- C4 (下加一线): staff_y = -6 ✅")
        print("- B4 (第三线): staff_y = 0 ✅")
        print("- C5 (第三间): staff_y = 1 ✅")
    else:
        print("❌ 部分音符位置映射有误")
    
    return all_correct

def test_position_calculation():
    """测试位置计算"""
    print("\n🧮 测试位置计算公式")
    print("=" * 30)
    
    # 模拟参数
    middle_line_y = 100  # 假设第三线在Y=100
    line_spacing = 15    # 线间距
    half_spacing = line_spacing // 2  # 7.5，实际为7
    
    print(f"参数: middle_line_y={middle_line_y}, line_spacing={line_spacing}, half_spacing={half_spacing}")
    print()
    
    # 测试关键音符
    test_notes = [
        ('C4', -6, "下加一线"),
        ('E4', -4, "第一线"),
        ('G4', -2, "第二线"),
        ('B4', 0, "第三线"),
        ('D5', 2, "第四线"),
        ('F5', 4, "第五线"),
        ('C5', 1, "第三间"),
        ('A4', -1, "第二间")
    ]
    
    print("音符\tstaff_y\t计算Y值\t相对位置\t描述")
    print("-" * 55)
    
    for note, staff_y, description in test_notes:
        calculated_y = middle_line_y - staff_y * half_spacing
        relative_pos = calculated_y - middle_line_y
        
        print(f"{note}\t{staff_y}\t{calculated_y}\t{relative_pos:+d}\t{description}")
    
    print(f"\n💡 说明:")
    print(f"- 第三线(B4)在Y={middle_line_y}")
    print(f"- 向上的音符Y值更小")
    print(f"- 向下的音符Y值更大")
    print(f"- 每个staff_y单位 = {half_spacing}像素")

def main():
    """主函数"""
    print("🎵 最终音符位置验证")
    print("验证是否恢复到原来正确的逻辑")
    print()
    
    # 运行测试
    result1 = test_staff_y_positions()
    test_position_calculation()
    
    print(f"\n{'='*50}")
    if result1:
        print("🎉 音符位置映射完全正确！")
        print("\n现在可以测试主程序:")
        print("python staff_practice_app_final.py")
        print("\n验证方法:")
        print("1. 选择'钢琴键盘'模式")
        print("2. 点击C4键，应该显示在下加一线")
        print("3. 点击B4键，应该显示在第三线（中间线）")
        print("4. 点击C5键，应该显示在第三间")
        print("5. 点击F5键，应该显示在第五线")
        print("\n✨ 特别注意:")
        print("- 第三间(C5)应该在第三线和第四线的正中间")
        print("- 这是您提到的关键问题点")
    else:
        print("⚠️ 音符位置映射仍有问题")
    
    return result1

if __name__ == "__main__":
    main()
