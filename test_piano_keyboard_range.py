#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试钢琴键盘音符范围是否正确
验证前三个音和整体范围
"""

def test_piano_keyboard_range():
    """测试钢琴键盘音符范围"""
    print("🎹 测试钢琴键盘音符范围")
    print("=" * 50)
    
    try:
        from staff_practice_app_final import NoteDatabase
        
        note_db = NoteDatabase()
        white_keys = note_db.get_white_keys()
        black_keys = note_db.get_black_keys()
        
        print(f"白键数量: {len(white_keys)}")
        print(f"黑键数量: {len(black_keys)}")
        print(f"总音符数量: {len(white_keys) + len(black_keys)}")
        
        print("\n白键序列:")
        for i, note in enumerate(white_keys):
            print(f"{i+1:2d}. {note.pitch} ({note.note_name}{note.octave})")
        
        print("\n黑键序列:")
        for i, note in enumerate(black_keys):
            print(f"{i+1:2d}. {note.pitch} ({note.note_name}#{note.octave})")
        
        # 验证前三个音
        print("\n🎯 验证前三个音:")
        if len(white_keys) >= 2 and len(black_keys) >= 1:
            first_white = white_keys[0]
            second_white = white_keys[1]
            first_black = black_keys[0]
            
            print(f"第1个白键: {first_white.pitch} (应该是A3)")
            print(f"第1个黑键: {first_black.pitch} (应该是A#3)")
            print(f"第2个白键: {second_white.pitch} (应该是B3)")
            
            # 检查是否正确
            correct_first_three = (
                first_white.pitch == 'A3' and
                first_black.pitch == 'A#3' and
                second_white.pitch == 'B3'
            )
            
            if correct_first_three:
                print("✅ 前三个音正确！")
            else:
                print("❌ 前三个音有误")
        
        # 验证范围
        print("\n🎯 验证音符范围:")
        if white_keys and black_keys:
            first_note = white_keys[0].pitch
            last_note = white_keys[-1].pitch
            
            print(f"起始音符: {first_note} (应该是A3)")
            print(f"结束音符: {last_note} (应该是C6)")
            
            correct_range = (first_note == 'A3' and last_note == 'C6')
            
            if correct_range:
                print("✅ 音符范围正确！")
            else:
                print("❌ 音符范围有误")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specific_notes():
    """测试特定音符"""
    print("\n🎵 测试特定音符")
    print("=" * 30)
    
    try:
        from staff_practice_app_final import NoteDatabase
        
        note_db = NoteDatabase()
        
        # 测试关键音符
        key_notes = ['A3', 'A#3', 'B3', 'C4', 'C#4', 'D4', 'A4', 'B4', 'C5', 'A5', 'B5', 'C6']
        
        print("关键音符验证:")
        for note_pitch in key_notes:
            note = note_db.get_note(note_pitch)
            if note:
                print(f"✅ {note_pitch}: {note.note_name}{note.octave}")
            else:
                print(f"❌ {note_pitch}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_keyboard_layout():
    """测试键盘布局"""
    print("\n⌨️  测试键盘布局")
    print("=" * 30)
    
    try:
        from staff_practice_app_final import PianoKeyboard
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建钢琴键盘
        canvas = tk.Canvas(root, width=750, height=120)
        piano = PianoKeyboard(canvas, 750, 120)
        
        print("✅ 钢琴键盘创建成功")
        
        # 检查键位映射
        key_positions = piano.key_positions
        print(f"键位映射数量: {len(key_positions)}")
        
        # 检查前几个键的位置
        first_keys = ['A3', 'A#3', 'B3', 'C4', 'C#4']
        print("\n前几个键的位置:")
        for key in first_keys:
            if key in key_positions:
                x, y, width, height, is_black = key_positions[key]
                key_type = "黑键" if is_black else "白键"
                print(f"  {key}: x={x}, width={width}, {key_type}")
            else:
                print(f"  {key}: 未找到")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎹 钢琴键盘音符范围验证")
    print("验证前三个音和整体范围是否正确")
    print()
    
    # 运行测试
    tests = [
        ("钢琴键盘音符范围", test_piano_keyboard_range),
        ("特定音符", test_specific_notes),
        ("键盘布局", test_keyboard_layout),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"\n✅ {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 钢琴键盘音符范围完全正确！")
        print("\n✨ 验证要点:")
        print("- 前三个音: A3(白键), A#3(黑键), B3(白键) ✅")
        print("- 音符范围: A3 到 C6 ✅")
        print("- 键盘布局: 保持原有样式 ✅")
        
        print("\n🚀 现在可以测试主程序:")
        print("python staff_practice_app_final.py")
        print("\n测试方法:")
        print("1. 选择'钢琴键盘'模式")
        print("2. 点击最左边的三个键，应该是A3, A#3, B3")
        print("3. 点击C4键，应该显示在下加一线")
        print("4. 验证音符范围从A3到C6")
    else:
        print("⚠️  钢琴键盘音符范围仍有问题")
    
    return passed == total

if __name__ == "__main__":
    main()
