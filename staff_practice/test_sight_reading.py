#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视奏模式的计分功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.note_model import note_db

def test_note_comparison():
    """测试音符比较逻辑"""
    print("=== 测试音符比较逻辑 ===")
    
    # 获取一些测试音符
    c4 = note_db.get_note('C4')
    c5 = note_db.get_note('C5')
    d4 = note_db.get_note('D4')
    
    print(f"C4: {c4.note_name}, 八度: {c4.octave}")
    print(f"C5: {c5.note_name}, 八度: {c5.octave}")
    print(f"D4: {d4.note_name}, 八度: {d4.octave}")
    
    # 测试比较逻辑
    def compare_notes(note1, note2):
        return (note1.note_name == note2.note_name and 
                note1.octave == note2.octave)
    
    print(f"\nC4 == C4: {compare_notes(c4, c4)}")
    print(f"C4 == C5: {compare_notes(c4, c5)}")
    print(f"C4 == D4: {compare_notes(c4, d4)}")

def test_random_notes():
    """测试随机音符生成"""
    print("\n=== 测试随机音符生成 ===")
    
    for i in range(5):
        note = note_db.get_random_white_key()
        print(f"{i+1}. {note.pitch}: {note.note_name}{note.octave} ({note.solfege})")

def main():
    """主测试函数"""
    print("🎵 视奏模式功能测试 🎵")
    print("=" * 40)
    
    test_note_comparison()
    test_random_notes()
    
    print("\n✅ 测试完成！")
    print("\n现在可以运行主程序测试视奏模式：")
    print("1. 选择'视奏练习'模式")
    print("2. 观察五线谱上的音符")
    print("3. 点击钢琴键盘上对应的键")
    print("4. 正确答案 +1 分，错误答案 -1 分")
    print("5. 正确时会自动生成新音符，错误时音符保持不变")

if __name__ == "__main__":
    main()
